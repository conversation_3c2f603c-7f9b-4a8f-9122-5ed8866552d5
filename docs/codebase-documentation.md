# Americans United Inc Shopify App - Codebase Documentation

## Overview

This is a comprehensive documentation of the Americans United Inc Shopify App codebase. The application is a print-on-demand management system that handles order processing, invoice calculations, shipping cost reconciliation, and ShipStation integration for multiple Shopify stores.

## Architecture Overview

The application follows a hybrid architecture with:
- **Remix Frontend**: React-based UI with server-side rendering
- **Express Multistore Server**: Handles cross-store operations and webhooks
- **Service-Oriented Business Logic**: Centralized business operations
- **Repository Pattern**: Data access abstraction
- **DTO Pattern**: Data transformation and validation

## Root Configuration Files

### `package.json`
**Purpose**: Project configuration and dependency management
**Key Scripts**:
- `dev`: Start development server with Shopify CLI
- `build`: Build production application
- `start`: Start production Remix server
- `start:express`: Start Express multistore server
- `start:both`: Run both servers concurrently
- `test`: Run Jest test suite
- `prisma`: Database operations

### `fly.toml`
**Purpose**: Fly.io deployment configuration
**Key Sections**:
- Multi-process configuration for Remix and Express servers
- Environment variables and secrets
- Health check endpoints
- Resource allocation

### `Dockerfile.multiprocess`
**Purpose**: Multi-process container definition
**Features**:
- Node.js 18 Alpine base image
- Production optimizations
- Multi-stage build process
- Process management with start.sh

### `start.sh`
**Purpose**: Process startup script for production
**Functions**:
- Database migration execution
- Concurrent server startup
- Health check initialization

## Core Application Structure

### `app/` - Remix Application

#### `app/root.jsx`
**Purpose**: Root React component and HTML structure
**Functions**:
- `App()`: Main application wrapper with meta tags, fonts, and favicon

#### `app/entry.server.jsx`
**Purpose**: Server-side rendering entry point
**Functions**:
- `handleRequest()`: Handles SSR with streaming and bot detection
- `streamTimeout`: 5-second timeout for React rendering

#### `app/db.server.js`
**Purpose**: Database client initialization and connection management
**Functions**:
- `createPrismaClient()`: Creates optimized Prisma client
- Global instance management for development
- Graceful shutdown handling

#### `app/shopify.server.js`
**Purpose**: Shopify app configuration and authentication
**Functions**:
- `getDefaultStoreCredentials()`: Fallback credential resolution
- `shopifyApp()`: Main Shopify app instance configuration
- Session storage with Prisma
- REST API and GraphQL client setup

#### `app/shopify.multistore.server.js`
**Purpose**: Multistore authentication and management
**Functions**:
- `authenticateMultistore()`: Cross-store authentication
- `getStoreConfig()`: Store-specific configuration retrieval
- `createShopifyInstance()`: Dynamic Shopify client creation

### `app/routes/` - Route Handlers

#### `app/routes/app.jsx`
**Purpose**: Main application layout with navigation
**Functions**:
- `loader()`: Multistore authentication and admin detection
- `App()`: Navigation menu with conditional admin features
- `ErrorBoundary()`: Error handling for app routes

#### `app/routes/app._index.jsx`
**Purpose**: Home dashboard page
**Functions**:
- `loader()`: Authentication and setup status check
- `Index()`: Welcome page with feature overview

#### `app/routes/app.all-balances.jsx`
**Purpose**: Admin-only client balances overview
**Functions**:
- `loader()`: Fetch all shops, balances, and shipping costs
- `AllBalances()`: Data table with month filtering and totals calculation

#### `app/routes/app.invoice.jsx`
**Purpose**: Client invoice preview page
**Functions**:
- `loader()`: Fetch invoice balances and shipping costs for specific month
- `Invoice()`: Invoice display with month selection

#### `app/routes/app.shipstation.jsx`
**Purpose**: ShipStation integration management
**Functions**:
- `loader()`: Fetch store mappings and available stores
- `action()`: Handle store mapping updates
- `ShipStation()`: Store mapping interface

#### `app/routes/webhooks.app.jsx`
**Purpose**: Main Shopify webhook handler
**Functions**:
- `action()`: Process all Shopify webhooks (orders, app lifecycle)
- Order processing for CREATE, UPDATE, FULFILLED, CANCELLED
- App uninstall cleanup
- Webhook deduplication

#### `app/routes/webhooks.app.scopes_update.jsx`
**Purpose**: Handle scope update webhooks
**Functions**:
- `action()`: Update session scopes when app permissions change

### API Routes

#### `app/routes/api.scheduled-reconciliation.jsx`
**Purpose**: Trigger scheduled reconciliation jobs
**Functions**:
- `action()`: Execute reconciliation based on type parameter
- `loader()`: API status and running jobs information
- Supports: standard, check-missing, shipstation-sync, test-orders

#### `app/routes/api.shipping-costs.jsx`
**Purpose**: Fetch shipping costs for specific month/year
**Functions**:
- `loader()`: Authenticated shipping cost retrieval with month/year filters

#### `app/routes/api.shipstation-stores.jsx`
**Purpose**: Manage ShipStation store listings
**Functions**:
- `loader()`: Fetch available ShipStation stores
- `action()`: Refresh store listings from ShipStation API

#### `app/routes/api.shipstation-mapping.jsx`
**Purpose**: Handle ShipStation store mapping and sync operations
**Functions**:
- `loader()`: Get current store mappings
- `action()`: Create/update store mappings and trigger sync jobs
- Batch processing for large datasets
- Memory management for sync operations

#### `app/routes/api.shipstation-order-webhook.jsx`
**Purpose**: Process ShipStation order webhooks
**Functions**:
- `action()`: Handle ShipStation order notifications
- Extract carrier fees and update shipping costs
- Order tracking and processing status updates

#### `app/routes/api.shipstation-webhook.jsx`
**Purpose**: Process ShipStation shipping webhooks
**Functions**:
- `action()`: Handle SHIP_NOTIFY webhooks
- Extract shipping costs from labels
- Update fulfillment costs in database

#### `app/routes/api.shipstation-sync.jsx`
**Purpose**: Manual ShipStation synchronization
**Functions**:
- `action()`: Trigger manual sync for specific store and date range
- Progress tracking and job management

#### `app/routes/api.shipstation-progress.jsx`
**Purpose**: Track ShipStation sync progress
**Functions**:
- `loader()`: Get current sync job status
- `updateShipStationProgress()`: Update job progress
- `completeShipStationJob()`: Mark job as completed
- `failShipStationJob()`: Mark job as failed

### `app/models/` - Data Access Layer

#### `app/models/Shop.server.js`
**Purpose**: Shop data operations
**Functions**:
- `getShops()`: Retrieve all unique shops from sessions
- `getShop(shop)`: Get specific shop with access token

#### `app/models/InvoiceBalance.server.js`
**Purpose**: Invoice balance management
**Functions**:
- `getInvoiceBalance(shop, category, month, year)`: Get specific balance
- `getInvoiceBalances(shop)`: Get all balances for shop
- `getInvoiceBalancesByMonth(shop, month, year)`: Monthly balances
- `getAllInvoiceBalances()`: Admin view of all balances
- `createInvoiceBalance()`: Create new balance record
- `setInvoiceBalance()`: Create or update balance
- `deleteInvoiceBalance(id)`: Remove balance record

#### `app/models/Price.server.js`
**Purpose**: Pricing data management
**Functions**:
- `getPrice(shop, category)`: Get price for specific category
- `getAllPrices(shop)`: Get all prices for shop
- `createPrice(shop, category, price)`: Create new price
- `updatePrice(shop, category, price)`: Update existing price
- `deletePrice(shop, category)`: Remove price record
- `getUniqueShopsWithPrices()`: Get shops that have pricing data

#### `app/models/ShippingCost.server.js`
**Purpose**: Shipping cost tracking
**Functions**:
- `getShippingCostsByMonth(shop, month, year)`: Monthly shipping costs
- `getAllShippingCosts()`: Admin view of all shipping costs
- `updateShippingCost()`: Update shipping cost records
- `updateFulfillmentCost()`: Update fulfillment cost records
- `createShippingTransaction()`: Log shipping cost transactions

#### `app/models/ProcessedWebhook.server.js`
**Purpose**: Webhook deduplication and tracking
**Functions**:
- `hasProcessedWebhook(shop, webhookId)`: Check if webhook processed
- `markWebhookProcessed()`: Mark webhook as processed
- `hasProcessedOrder(shop, orderId)`: Check if order processed
- `checkAndMarkEventProcessed()`: Event-based deduplication
- `markOrderProcessed()`: Mark order as manually processed

#### `app/models/IsSetup.server.js`
**Purpose**: Shop setup status management
**Functions**:
- `getSetupFlag(shop)`: Get setup status
- `createSetupFlag(shop)`: Initialize setup flag
- `setSetupFlag(shop, isSetup)`: Update setup status
- `checkAndMarkProcessing(shop)`: Atomic processing flag management
- `markSetupCompleted(shop)`: Complete setup process
- `resetProcessingFlag(shop)`: Reset processing state

#### `app/models/ReconciliationJob.server.js`
**Purpose**: Reconciliation job management
**Functions**:
- `createReconciliationJob()`: Start new reconciliation job
- `updateReconciliationJob()`: Update job progress
- `completeReconciliationJob()`: Mark job as completed
- `getRunningReconciliationJobs()`: Get active jobs
- `getReconciliationJobsByShop()`: Shop-specific job history

#### `app/models/ShipStationStoreMapping.server.js`
**Purpose**: ShipStation store mapping management
**Functions**:
- `getStoreMappingsByShop(shop)`: Get mappings for shop
- `getStoreMappingByStoreId(storeId)`: Get mapping by store ID
- `createOrUpdateStoreMapping()`: Create or update mapping
- `deleteStoreMapping()`: Remove mapping

### `app/services/` - Business Logic Layer

#### `app/services/order/OrderProcessingService.js`
**Purpose**: Core order processing orchestration
**Functions**:
- `processOrder()`: Main order processing workflow
- `processOrderInternal()`: Internal processing logic
- `validateOrder()`: Order validation
- `calculateBalances()`: Balance calculations
- `updateInvoiceBalances()`: Update balance records

#### `app/services/reconciliation/ReconciliationOrchestrator.js`
**Purpose**: Reconciliation workflow coordination
**Functions**:
- `executeReconciliation()`: Main reconciliation workflow
- `scheduleReconciliation()`: Schedule reconciliation jobs
- `processReconciliationBatch()`: Batch processing
- `generateReconciliationReport()`: Create reconciliation reports

### `app/utils/` - Utility Functions

#### `app/utils/balance.js`
**Purpose**: Balance calculation utilities
**Functions**:
- `calculateTotalBalance(balances)`: Calculate total from balance array

#### `app/utils/order-processing.js`
**Purpose**: Order processing utilities (Legacy - being refactored)
**Functions**:
- Order validation and processing logic
- SKU parsing and categorization
- Price calculations

#### `app/utils/reconciliation.js`
**Purpose**: Reconciliation utilities (Legacy - being refactored)
**Functions**:
- Missing order detection
- Data consistency checks
- Reconciliation reporting

#### `app/utils/shipstation-api.js`
**Purpose**: ShipStation API utilities (Legacy - being replaced)
**Functions**:
- API client for ShipStation v1 and v2
- Rate limiting and retry logic
- Data transformation

#### `app/utils/fulfillment-order-service.js`
**Purpose**: Fulfillment service validation
**Functions**:
- `getFulfillmentService()`: Get variant fulfillment service
- `isFulfillmentServiceManual()`: Check if manual fulfillment
- Shopify REST API integration

#### `app/utils/memory-cache.js`
**Purpose**: In-memory caching utilities
**Functions**:
- `get(key)`: Retrieve cached value
- `set(key, value, expiration)`: Store value with TTL
- `del(key)`: Remove cached value
- `clear()`: Clear all cache
- `getStats()`: Cache statistics

#### `app/utils/database-health.js`
**Purpose**: Database health monitoring
**Functions**:
- `checkConnection()`: Test database connectivity
- `collectMetrics()`: Gather database metrics
- `testMigrationReadiness()`: Validate migration prerequisites
- `validateDataIntegrity()`: Check data consistency

### `app/lib/` - Core Infrastructure

#### `app/lib/config/index.js`
**Purpose**: Centralized configuration management
**Functions**:
- Configuration validation with Zod schemas
- Multistore configuration parsing
- Environment variable management

#### `app/lib/container/ServiceContainer.js`
**Purpose**: Dependency injection container
**Functions**:
- `register(name, factory, singleton)`: Register service
- `resolve(name)`: Resolve service instance
- Singleton pattern support

#### `app/lib/api/base/ApiClient.js`
**Purpose**: Base API client with retry logic
**Functions**:
- HTTP client with timeout and retry
- Rate limiting integration
- Error handling and logging

#### `app/lib/api/shopify/ShopifyClient.js`
**Purpose**: Shopify-specific API wrapper
**Functions**:
- GraphQL and REST API clients
- Authentication handling
- Rate limit management

#### `app/lib/api/shipstation/ShipStationClient.js`
**Purpose**: Unified ShipStation API client
**Functions**:
- V1 and V2 API integration
- Webhook processing
- Data transformation

### `app/dto/` - Data Transfer Objects

#### `app/dto/order/OrderDTO.js`
**Purpose**: Order data standardization
**Functions**:
- `fromShopifyOrder()`: Convert Shopify order to DTO
- `validate()`: Validate order data
- `toObject()`: Convert to plain object

#### `app/dto/order/LineItemDTO.js`
**Purpose**: Line item data standardization
**Functions**:
- `fromShopifyLineItem()`: Convert Shopify line item to DTO
- `calculateTotals()`: Calculate line item totals
- `validate()`: Validate line item data

### `express-server/` - Multistore Server

#### `express-server/server.js`
**Purpose**: Express server for multistore operations
**Functions**:
- Multistore middleware setup
- CORS and security configuration
- Health check endpoints
- Route registration

#### `express-server/routes/auth.js`
**Purpose**: Multistore authentication routes
**Functions**:
- OAuth flow for multiple stores
- Session management
- Store-specific authentication

#### `express-server/routes/webhooks.js`
**Purpose**: Multistore webhook handlers
**Functions**:
- Cross-store webhook processing
- Webhook validation and routing
- Store-specific webhook handling

#### `express-server/routes/api.js`
**Purpose**: Multistore API endpoints
**Functions**:
- Cross-store data access
- Aggregated reporting
- Store management operations

### `shared/` - Shared Utilities

#### `shared/utils/index.js`
**Purpose**: Common utility functions
**Functions**:
- `formatErrorResponse()`: Standardize error responses
- `formatSuccessResponse()`: Standardize success responses
- `normalizeShopDomain()`: Normalize shop domain format
- `formatShopName()`: Format shop name for display
- `validateRequiredFields()`: Validate required fields
- `safeJsonParse()`: Safe JSON parsing
- `delay()`: Promise-based delay
- `retryWithBackoff()`: Retry with exponential backoff
- `chunkArray()`: Split array into chunks
- `generateRequestId()`: Generate unique request ID
- `isValidUrl()`: URL validation
- `sanitizeForLogging()`: Remove sensitive data for logging
- `formatBytes()`: Format byte sizes
- `getMemoryUsage()`: Get memory usage statistics
- `isMemoryUsageHigh()`: Check memory usage threshold

#### `shared/utils/processLargeDataset.js`
**Purpose**: Memory-efficient large dataset processing
**Functions**:
- `processLargeDataset()`: Batch process large arrays
- `processDataStream()`: Stream processing for very large datasets
- `transformArraySafely()`: Memory-safe array transformation
- `monitorMemoryUsage()`: Memory usage monitoring during processing

#### `shared/constants/index.js`
**Purpose**: Application constants
**Exports**:
- HTTP status codes
- Memory thresholds
- Timeout values
- API limits

### `tests/` - Test Suite

#### Test Structure:
- `unit/`: Unit tests for individual functions
- `integration/`: Integration tests for API endpoints
- `e2e/`: End-to-end workflow tests
- `performance/`: Performance and load tests
- `fixtures/`: Test data and mocks

### Configuration Files

#### `prisma/schema.prisma`
**Purpose**: Database schema definition
**Models**:
- Session, SetupFlag, BalanceGroup
- InvoiceBalance, InvoiceTransaction
- ShippingCost, ShippingTransaction
- Price, Unprocessable, ProcessedWebhook
- ReconciliationJob, ShipStationStoreMapping
- ShipStationOrderTracking, VariantFulfillmentService

#### `jest.config.js`
**Purpose**: Jest testing configuration
**Features**:
- ES modules support
- Test environment setup
- Coverage reporting
- Mock configurations

#### `.graphqlrc.js`
**Purpose**: GraphQL code generation configuration
**Features**:
- Shopify Admin API integration
- Type generation for GraphQL queries
- Document scanning and validation

## Key Business Logic Flows

### Order Processing Flow
1. Webhook received → `webhooks.app.jsx`
2. Deduplication check → `ProcessedWebhook.server.js`
3. Order processing → `OrderProcessingService.js`
4. Balance calculation → `balance.js`
5. Database update → `InvoiceBalance.server.js`

### Reconciliation Flow
1. Trigger → `api.scheduled-reconciliation.jsx`
2. Job creation → `ReconciliationJob.server.js`
3. Orchestration → `ReconciliationOrchestrator.js`
4. Data processing → Various processors
5. Report generation → Reconciliation reporters

### ShipStation Integration Flow
1. Store mapping → `app.shipstation.jsx`
2. Webhook processing → `api.shipstation-webhook.jsx`
3. Cost extraction → ShipStation API utilities
4. Database update → `ShippingCost.server.js`

This documentation provides a comprehensive overview of the codebase structure and functionality. Each file serves a specific purpose in the overall application architecture, from handling user requests to processing business logic and managing data persistence.
