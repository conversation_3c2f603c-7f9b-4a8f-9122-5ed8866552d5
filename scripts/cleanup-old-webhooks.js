#!/usr/bin/env node

/**
 * Cleanup Old Webhooks Script
 *
 * This script helps identify and remove old webhook subscriptions that are causing 503 errors.
 * It will list all webhooks for each store and allow you to delete specific ones.
 */

import { PrismaClient } from '@prisma/client';

// Use environment variable for database URL, fallback to production URL
const databaseUrl = process.env.DATABASE_URL || "postgresql://postgres:<EMAIL>:5432/americans_united_inc";

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: databaseUrl
    }
  }
});

// Store configurations from your multistore setup
const STORES = [
  'phaselineco-fulfillment.myshopify.com',
  'americansunitedinc.myshopify.com',
  'american-trigger-pullers.myshopify.com',
  'otx-fulfillment.myshopify.com',
  'pltdaddy.myshopify.com'
];

/**
 * Get access token for a store from the database
 */
async function getStoreAccessToken(shop) {
  try {
    const session = await prisma.session.findFirst({
      where: {
        shop: shop
      },
      orderBy: {
        expires: 'desc'
      }
    });

    if (!session || !session.accessToken) {
      console.error(`❌ No valid session found for ${shop}`);
      return null;
    }

    return session.accessToken;
  } catch (error) {
    console.error(`❌ Error getting access token for ${shop}:`, error.message);
    return null;
  }
}

/**
 * Make a REST API call to Shopify
 */
async function shopifyRestCall(shop, accessToken, endpoint, method = 'GET', data = null) {
  const url = `https://${shop}/admin/api/2025-01${endpoint}`;

  const options = {
    method,
    headers: {
      'X-Shopify-Access-Token': accessToken,
      'Content-Type': 'application/json',
    }
  };

  if (data && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`❌ API call failed for ${shop}:`, error.message);
    throw error;
  }
}

/**
 * List all webhooks for a store
 */
async function listWebhooks(shop, accessToken) {
  try {
    console.log(`\n📋 Listing webhooks for ${shop}...`);

    const response = await shopifyRestCall(shop, accessToken, '/webhooks.json');
    const webhooks = response.webhooks || [];

    if (webhooks.length === 0) {
      console.log(`   No webhooks found for ${shop}`);
      return [];
    }

    console.log(`   Found ${webhooks.length} webhooks:`);

    webhooks.forEach((webhook, index) => {
      const createdAt = new Date(webhook.created_at).toLocaleDateString();
      const isOldUrl = webhook.address && webhook.address.includes('aui-invoice-preview.fly.dev');
      const status = isOldUrl ? '🔴 OLD URL' : '✅ Current';

      console.log(`   ${index + 1}. ID: ${webhook.id}`);
      console.log(`      Topic: ${webhook.topic}`);
      console.log(`      Address: ${webhook.address}`);
      console.log(`      Created: ${createdAt}`);
      console.log(`      Status: ${status}`);
      console.log(`      API Version: ${webhook.api_version || 'N/A'}`);
      console.log('');
    });

    return webhooks;
  } catch (error) {
    console.error(`❌ Failed to list webhooks for ${shop}:`, error.message);
    return [];
  }
}

/**
 * Delete a specific webhook
 */
async function deleteWebhook(shop, accessToken, webhookId) {
  try {
    console.log(`🗑️  Deleting webhook ${webhookId} from ${shop}...`);

    await shopifyRestCall(shop, accessToken, `/webhooks/${webhookId}.json`, 'DELETE');

    console.log(`✅ Successfully deleted webhook ${webhookId} from ${shop}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to delete webhook ${webhookId} from ${shop}:`, error.message);
    return false;
  }
}

/**
 * Main function to analyze and clean up webhooks
 */
async function main() {
  console.log('🧹 Starting webhook cleanup analysis...\n');

  const allWebhooks = {};
  const oldWebhooks = [];

  // First, list all webhooks for all stores
  for (const shop of STORES) {
    console.log(`\n🏪 Processing store: ${shop}`);

    const accessToken = await getStoreAccessToken(shop);
    if (!accessToken) {
      console.log(`   ⏭️  Skipping ${shop} - no access token`);
      continue;
    }

    const webhooks = await listWebhooks(shop, accessToken);
    allWebhooks[shop] = { webhooks, accessToken };

    // Identify old webhooks pointing to the old URL
    const oldWebhooksForStore = webhooks.filter(webhook =>
      webhook.address && webhook.address.includes('aui-invoice-preview.fly.dev')
    );

    if (oldWebhooksForStore.length > 0) {
      console.log(`   🔴 Found ${oldWebhooksForStore.length} old webhooks pointing to aui-invoice-preview.fly.dev`);
      oldWebhooks.push(...oldWebhooksForStore.map(w => ({ ...w, shop })));
    }
  }

  // Summary
  console.log('\n📊 SUMMARY');
  console.log('='.repeat(50));

  if (oldWebhooks.length === 0) {
    console.log('✅ No old webhooks found pointing to aui-invoice-preview.fly.dev');
    console.log('   The 503 errors might be coming from a different source.');
  } else {
    console.log(`🔴 Found ${oldWebhooks.length} old webhooks that need cleanup:`);

    oldWebhooks.forEach((webhook, index) => {
      console.log(`   ${index + 1}. ${webhook.shop} - ID: ${webhook.id} - Topic: ${webhook.topic}`);
    });

    console.log('\n🤔 WHAT TO DO NEXT:');
    console.log('1. Review the old webhooks listed above');
    console.log('2. Run this script with --delete flag to remove them:');
    console.log('   node scripts/cleanup-old-webhooks.js --delete');
    console.log('3. Or delete specific webhooks by ID:');
    console.log('   node scripts/cleanup-old-webhooks.js --delete-id=WEBHOOK_ID --shop=SHOP_DOMAIN');
  }

  // Handle deletion if requested
  const args = process.argv.slice(2);
  const shouldDeleteAll = args.includes('--delete');
  const deleteIdArg = args.find(arg => arg.startsWith('--delete-id='));
  const shopArg = args.find(arg => arg.startsWith('--shop='));

  if (shouldDeleteAll && oldWebhooks.length > 0) {
    console.log('\n🗑️  DELETING ALL OLD WEBHOOKS...');

    for (const webhook of oldWebhooks) {
      const storeData = allWebhooks[webhook.shop];
      if (storeData) {
        await deleteWebhook(webhook.shop, storeData.accessToken, webhook.id);
        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    console.log('\n✅ Cleanup complete!');
  } else if (deleteIdArg && shopArg) {
    const webhookId = deleteIdArg.split('=')[1];
    const shop = shopArg.split('=')[1];

    const storeData = allWebhooks[shop];
    if (storeData) {
      await deleteWebhook(shop, storeData.accessToken, webhookId);
    } else {
      console.error(`❌ Store ${shop} not found or no access token available`);
    }
  }

  await prisma.$disconnect();
}

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
