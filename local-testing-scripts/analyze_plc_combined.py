import csv
from datetime import datetime

print("=== COMBINED PHASELINECO-FULFILLMENT CSV ANALYSIS ===")

# Analyze May CSV
may_total_carrier_fee = 0
may_total_shipments = 0
may_valid_non_draft_carrier_fee = 0
may_valid_non_draft_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/plc-shipments-5-25.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        carrier_fee = float(row['Carrier - Fee'])
        void_flag = row['Shipment - Void Flag']
        order_source = row['Order - Source']
        
        may_total_carrier_fee += carrier_fee
        may_total_shipments += 1
        
        # Only count valid non-draft orders
        if void_flag.lower() != 'true' and order_source != 'shopify_draft_order':
            may_valid_non_draft_carrier_fee += carrier_fee
            may_valid_non_draft_shipments += 1

# Analyze June CSV
june_total_carrier_fee = 0
june_total_shipments = 0
june_valid_non_draft_carrier_fee = 0
june_valid_non_draft_shipments = 0

with open('/home/<USER>/workspace/shopify-apps/americans-united-inc/test-data/plc-shipments-june.csv', 'r') as file:
    reader = csv.DictReader(file)
    for row in reader:
        carrier_fee = float(row['Carrier Fee'])
        
        june_total_carrier_fee += carrier_fee
        june_total_shipments += 1
        
        # Assume all June shipments are valid non-draft
        june_valid_non_draft_carrier_fee += carrier_fee
        june_valid_non_draft_shipments += 1

print("=== MAY CSV DATA ===")
print(f"Total shipments: {may_total_shipments}")
print(f"Total carrier fees: ${may_total_carrier_fee:.2f}")
print(f"Valid non-draft shipments: {may_valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${may_valid_non_draft_carrier_fee:.2f}")

print("\n=== JUNE CSV DATA ===")
print(f"Total shipments: {june_total_shipments}")
print(f"Total carrier fees: ${june_total_carrier_fee:.2f}")
print(f"Valid non-draft shipments: {june_valid_non_draft_shipments}")
print(f"Valid non-draft carrier fees: ${june_valid_non_draft_carrier_fee:.2f}")

print("\n=== COMBINED CSV DATA ===")
combined_shipments = may_valid_non_draft_shipments + june_valid_non_draft_shipments
combined_carrier_fees = may_valid_non_draft_carrier_fee + june_valid_non_draft_carrier_fee

print(f"Combined valid non-draft shipments: {combined_shipments}")
print(f"Combined valid non-draft carrier fees: ${combined_carrier_fees:.2f}")

markup = combined_carrier_fees * 0.10
total_with_markup = combined_carrier_fees + markup
fulfillment_costs = combined_shipments * 1.50
expected_total = total_with_markup + fulfillment_costs

print(f"Expected 10% markup: ${markup:.2f}")
print(f"Expected total with markup: ${total_with_markup:.2f}")
print(f"Expected fulfillment costs ({combined_shipments} x $1.50): ${fulfillment_costs:.2f}")
print(f"Expected grand total: ${expected_total:.2f}")

print("\n=== COMPARISON WITH DATABASE RESULTS ===")
print("Database showed:")
print("  May 2025: 1766 items, $11,946.39 base, $13,141.03 total")
print("  June 2025: 2357 items, $16,122.25 base, $17,734.47 total")
print("  Combined: 4123 items, $28,068.64 base, $30,875.50 total")

db_combined_items = 4123
db_combined_base = 28068.64
db_combined_total = 30875.50

print(f"\nCSV vs DB differences:")
print(f"  Quantity: {db_combined_items - combined_shipments} ({db_combined_items} DB vs {combined_shipments} CSV)")
print(f"  Base cost: ${db_combined_base - combined_carrier_fees:.2f} (${db_combined_base:.2f} DB vs ${combined_carrier_fees:.2f} CSV)")
print(f"  Total cost: ${db_combined_total - total_with_markup:.2f} (${db_combined_total:.2f} DB vs ${total_with_markup:.2f} CSV)")

if abs(db_combined_items - combined_shipments) < 10 and abs(db_combined_base - combined_carrier_fees) < 100:
    print("\n✅ EXCELLENT MATCH! The ShipStation integration is working correctly.")
    print("The small differences are likely due to:")
    print("  - Additional shipments processed after CSV export")
    print("  - Minor timing differences in data collection")
else:
    print("\n❌ SIGNIFICANT DISCREPANCY DETECTED")
    print("Further investigation needed.")
