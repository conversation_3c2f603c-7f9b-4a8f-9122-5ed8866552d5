# Shopify App Configuration for PLD App Development Store
# Store: pld-app-development-store.myshopify.com

client_id = "8c37f2753cfd7a2bafdf5f6bdfd17557"
application_url = "https://satyr-up-vulture.ngrok-free.app/"
embedded = true
name = "americans-united-inc-pld-app"
handle = "americans-united-inc-pld-app"

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "orders/create", "products/update" ]
  uri = "/webhooks/app"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_products, read_orders, write_orders, merchant_managed_fulfillment_orders, third_party_fulfillment_orders"

[auth]
redirect_urls = [
  "https://satyr-up-vulture.ngrok-free.app/auth/callback",
  "https://satyr-up-vulture.ngrok-free.app/auth/shopify/callback",
  "https://satyr-up-vulture.ngrok-free.app/api/auth/callback"
]

[pos]
embedded = false

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true
