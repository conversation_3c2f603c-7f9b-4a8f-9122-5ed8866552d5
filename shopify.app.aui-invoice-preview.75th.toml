# Shopify App Configuration for 75th RRA App Development Store
# Store: 75th-rra-app-development-store.myshopify.com

client_id = "6a5f4bd11e96c9433fdca65fa1b7a2e2"
application_url = "https://americans-united-inc.fly.dev/"
embedded = true
name = "americans-united-inc-75th-rra"
handle = "americans-united-inc-75th-rra"

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "orders/create", "products/update" ]
  uri = "/webhooks/app"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_products, read_orders, write_orders, merchant_managed_fulfillment_orders, third_party_fulfillment_orders"

[auth]
redirect_urls = [
  "https://satyr-up-vulture.ngrok-free.app/auth/callback",
  "https://satyr-up-vulture.ngrok-free.app/auth/shopify/callback",
  "https://satyr-up-vulture.ngrok-free.app/api/auth/callback"
]

[pos]
embedded = false

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true
