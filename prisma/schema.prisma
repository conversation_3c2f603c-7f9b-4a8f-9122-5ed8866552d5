generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

model SetupFlag {
  shop         String  @id
  isSetup      Boolean
  isProcessing Boolean @default(false)
}

model BalanceGroup {
  id   Int    @id @default(autoincrement())
  shop String
  name String
}

model InvoiceBalance {
  id           Int                  @id @default(autoincrement())
  shop         String
  month        Int
  year         Int
  category     String
  quantity     Int
  balance      Decimal
  groupId      Int?
  transactions InvoiceTransaction[]

  @@unique([shop, category, month, year])
  @@index([shop, month, year])
  @@index([shop, category])
  @@index([groupId])
}

model InvoiceTransaction {
  id               Int            @id @default(autoincrement())
  invoiceBalanceId Int
  amount           Decimal
  description      String
  createdAt        DateTime       @default(now())
  metadata         String?
  invoiceBalance   InvoiceBalance @relation(fields: [invoiceBalanceId], references: [id], onDelete: Cascade)

  @@index([invoiceBalanceId])
}

model ShippingCost {
  id           Int                   @id @default(autoincrement())
  shop         String
  storeId      String
  month        Int
  year         Int
  quantity     Int
  shippingCost Decimal
  markupAmount Decimal
  totalAmount  Decimal
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  transactions ShippingTransaction[]

  @@index([shop, storeId])
  @@index([shop, month, year])
  @@index([shop, storeId, month, year])
  @@index([createdAt])
  @@index([updatedAt])
}

model ShippingTransaction {
  id             Int          @id @default(autoincrement())
  shippingCostId Int
  amount         Decimal
  shippingAmount Decimal
  markupAmount   Decimal
  description    String
  createdAt      DateTime     @default(now())
  metadata       String?
  shippingCost   ShippingCost @relation(fields: [shippingCostId], references: [id], onDelete: Cascade)

  @@index([shippingCostId])
}

model ShipStationStoreMapping {
  storeId   String   @id
  storeName String?
  shop      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([shop])
  @@index([storeName])
}

model ShipStationOrderTracking {
  storeId         String   @id
  shop            String
  lastOrderNumber String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([shop])
}

model Price {
  id       Int     @id @default(autoincrement())
  shop     String
  category String
  cost     Decimal

  @@unique([shop, category])
}

model Unprocessable {
  id          Int       @id @default(autoincrement())
  shop        String
  productType String?
  variant     String?
  sku         String?
  quantity    Int?
  date        DateTime?
  errorField  String
  message     String
  variantId   String?
  productId   String?
}

model ProcessedWebhook {
  id             Int      @id @default(autoincrement())
  shop           String
  topic          String
  webhookId      String
  eventId        String?
  orderId        String?
  subscriptionId String?
  processedAt    DateTime @default(now())

  @@unique([shop, webhookId])
  @@unique([shop, eventId])
  @@index([shop, orderId])
}

model ReconciliationJob {
  id              Int       @id @default(autoincrement())
  shop            String
  type            String
  status          String
  startDate       DateTime
  endDate         DateTime
  startedAt       DateTime  @default(now())
  completedAt     DateTime?
  ordersProcessed Int       @default(0)
  ordersSkipped   Int       @default(0)
  ordersFailed    Int       @default(0)
  error           String?

  @@index([shop, status])
  @@index([shop, startedAt])
  @@index([shop, type])
  @@index([status])
  @@index([startDate, endDate])
}

model VariantFulfillmentService {
  variantId          String   @id
  fulfillmentService String
  lastUpdated        DateTime @default(now())

  @@index([lastUpdated])
}

model InventoryRequirement {
  id               Int                    @id @default(autoincrement())
  shop             String
  garmentCode      String
  color            String
  size             String
  category         String
  requiredQuantity Int                    @default(0)
  createdAt        DateTime               @default(now())
  updatedAt        DateTime               @updatedAt
  transactions     InventoryTransaction[]

  @@unique([shop, garmentCode, color, size, category])
  @@index([shop])
  @@index([category])
  @@index([garmentCode])
  @@index([shop, category])
}

model InventoryTransaction {
  id                     Int                  @id @default(autoincrement())
  inventoryRequirementId Int
  orderId                String
  lineItemId             String?
  quantityChange         Int
  transactionType        String
  sku                    String?
  productTitle           String?
  variantTitle           String?
  createdAt              DateTime             @default(now())
  metadata               String?
  inventoryRequirement   InventoryRequirement @relation(fields: [inventoryRequirementId], references: [id], onDelete: Cascade)

  @@index([inventoryRequirementId])
  @@index([orderId])
  @@index([transactionType])
  @@index([createdAt])
}
