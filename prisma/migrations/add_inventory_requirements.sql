-- Migration to add inventory requirements tracking tables

-- Create InventoryRequirement table for tracking garment inventory needs
CREATE TABLE "InventoryRequirement" (
    "id" SERIAL NOT NULL,
    "shop" TEXT NOT NULL,
    "garmentCode" TEXT NOT NULL,
    "color" TEXT NOT NULL,
    "size" TEXT NOT NULL,
    "category" TEXT NOT NULL, -- 'regular' or 'engraving'
    "requiredQuantity" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InventoryRequirement_pkey" PRIMARY KEY ("id")
);

-- Create InventoryTransaction table for tracking changes to inventory requirements
CREATE TABLE "InventoryTransaction" (
    "id" SERIAL NOT NULL,
    "inventoryRequirementId" INTEGER NOT NULL,
    "orderId" TEXT NOT NULL,
    "lineItemId" TEXT,
    "quantityChange" INTEGER NOT NULL, -- positive for orders, negative for fulfillments
    "transactionType" TEXT NOT NULL, -- 'order_created', 'order_fulfilled', 'order_cancelled'
    "sku" TEXT,
    "productTitle" TEXT,
    "variantTitle" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" TEXT, -- JSON string for additional data

    CONSTRAINT "InventoryTransaction_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint for inventory requirements (one record per shop/garment/color/size/category combination)
CREATE UNIQUE INDEX "InventoryRequirement_shop_garmentCode_color_size_category_key" ON "InventoryRequirement"("shop", "garmentCode", "color", "size", "category");

-- Create indexes for performance
CREATE INDEX "InventoryRequirement_shop_idx" ON "InventoryRequirement"("shop");
CREATE INDEX "InventoryRequirement_category_idx" ON "InventoryRequirement"("category");
CREATE INDEX "InventoryRequirement_garmentCode_idx" ON "InventoryRequirement"("garmentCode");
CREATE INDEX "InventoryRequirement_shop_category_idx" ON "InventoryRequirement"("shop", "category");

CREATE INDEX "InventoryTransaction_inventoryRequirementId_idx" ON "InventoryTransaction"("inventoryRequirementId");
CREATE INDEX "InventoryTransaction_orderId_idx" ON "InventoryTransaction"("orderId");
CREATE INDEX "InventoryTransaction_transactionType_idx" ON "InventoryTransaction"("transactionType");
CREATE INDEX "InventoryTransaction_createdAt_idx" ON "InventoryTransaction"("createdAt");

-- Add foreign key constraint
ALTER TABLE "InventoryTransaction" ADD CONSTRAINT "InventoryTransaction_inventoryRequirementId_fkey" FOREIGN KEY ("inventoryRequirementId") REFERENCES "InventoryRequirement"("id") ON DELETE CASCADE ON UPDATE CASCADE;
