/**
 * Inventory Calculation Service
 *
 * This service handles the core logic for calculating and tracking inventory requirements
 * based on orders and fulfillments. It processes order line items and updates inventory
 * requirements accordingly.
 */

import { SkuParsingService } from './SkuParsingService.js';
import { BusinessLogicError } from '../../lib/errors/AppError.js';

export class InventoryCalculationService {
  constructor(dependencies = {}) {
    this.dependencies = dependencies;
    this.prisma = null;
    this.skuParser = new SkuParsingService();
    this.initialized = false;
  }

  async initialize() {
    if (!this.initialized) {
      if (this.dependencies.prisma) {
        this.prisma = this.dependencies.prisma;
      } else {
        // Import the database directly
        const dbModule = await import('../../db.server.js');
        this.prisma = dbModule.default;
      }
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  /**
   * Process an order and update inventory requirements
   * @param {object} order - Shopify order object
   * @param {string} transactionType - Type of transaction ('order_created', 'order_fulfilled', 'order_cancelled')
   * @returns {Promise<object>} - Processing results
   */
  async processOrderForInventory(order, transactionType = 'order_created') {
    try {
      const shop = this.extractShopFromOrder(order);
      const results = {
        processed: 0,
        skipped: 0,
        errors: [],
        inventoryUpdates: []
      };

      // Process each line item in the order
      for (const lineItem of order.line_items || []) {
        try {
          const result = await this.processLineItem(
            shop,
            order.id.toString(),
            lineItem,
            transactionType
          );

          if (result.skipped) {
            results.skipped++;
          } else {
            results.processed++;
            results.inventoryUpdates.push(result);
          }
        } catch (error) {
          results.errors.push({
            lineItemId: lineItem.id,
            sku: lineItem.sku,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to process order for inventory: ${error.message}`,
        { orderId: order.id, transactionType }
      );
    }
  }

  /**
   * Process a single line item for inventory requirements
   * @param {string} shop - Shop identifier
   * @param {string} orderId - Order ID
   * @param {object} lineItem - Order line item
   * @param {string} transactionType - Transaction type
   * @returns {Promise<object>} - Processing result
   */
  async processLineItem(shop, orderId, lineItem, transactionType) {
    // Skip items with non-manual fulfillment service
    if (lineItem.fulfillment_service !== 'manual') {
      return { skipped: true, reason: 'Non-manual fulfillment service' };
    }

    // Parse SKU to extract inventory information
    const parseResult = this.skuParser.parseSkuForInventory(
      lineItem.sku,
      lineItem.variant_title,
      lineItem.product_type || ''
    );

    if (parseResult.shouldSkip) {
      return { skipped: true, reason: parseResult.reason };
    }

    // Calculate quantity change based on transaction type
    let quantityChange = 0;
    switch (transactionType) {
      case 'order_created':
        quantityChange = lineItem.quantity;
        break;
      case 'order_fulfilled':
        quantityChange = -lineItem.quantity; // Negative for fulfillments
        break;
      case 'order_cancelled':
        quantityChange = -lineItem.quantity; // Negative for cancellations
        break;
      default:
        throw new BusinessLogicError(`Unknown transaction type: ${transactionType}`);
    }

    // Update or create inventory requirement
    const inventoryRequirement = await this.updateInventoryRequirement(
      shop,
      parseResult.garmentCode,
      parseResult.color,
      parseResult.size,
      parseResult.category,
      quantityChange
    );

    // Create inventory transaction record
    const transaction = await this.createInventoryTransaction(
      inventoryRequirement.id,
      orderId,
      lineItem.id?.toString(),
      quantityChange,
      transactionType,
      lineItem
    );

    return {
      skipped: false,
      inventoryRequirement,
      transaction,
      parseResult
    };
  }

  /**
   * Update or create inventory requirement record
   * @param {string} shop - Shop identifier
   * @param {string} garmentCode - Garment code
   * @param {string} color - Color
   * @param {string} size - Size
   * @param {string} category - Category ('regular' or 'engraving')
   * @param {number} quantityChange - Change in required quantity
   * @returns {Promise<object>} - Inventory requirement record
   */
  async updateInventoryRequirement(shop, garmentCode, color, size, category, quantityChange) {
    try {
      await this.ensureInitialized();

      // Use upsert to create or update the inventory requirement
      const inventoryRequirement = await this.prisma.inventoryRequirement.upsert({
        where: {
          shop_garmentCode_color_size_category: {
            shop,
            garmentCode,
            color,
            size,
            category
          }
        },
        update: {
          requiredQuantity: {
            increment: quantityChange
          },
          updatedAt: new Date()
        },
        create: {
          shop,
          garmentCode,
          color,
          size,
          category,
          requiredQuantity: Math.max(0, quantityChange) // Don't allow negative initial quantities
        }
      });

      return inventoryRequirement;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to update inventory requirement: ${error.message}`,
        { shop, garmentCode, color, size, category, quantityChange }
      );
    }
  }

  /**
   * Create inventory transaction record
   * @param {number} inventoryRequirementId - Inventory requirement ID
   * @param {string} orderId - Order ID
   * @param {string} lineItemId - Line item ID
   * @param {number} quantityChange - Quantity change
   * @param {string} transactionType - Transaction type
   * @param {object} lineItem - Original line item data
   * @returns {Promise<object>} - Transaction record
   */
  async createInventoryTransaction(
    inventoryRequirementId,
    orderId,
    lineItemId,
    quantityChange,
    transactionType,
    lineItem
  ) {
    try {
      await this.ensureInitialized();

      const transaction = await this.prisma.inventoryTransaction.create({
        data: {
          inventoryRequirementId,
          orderId,
          lineItemId,
          quantityChange,
          transactionType,
          sku: lineItem.sku,
          productTitle: lineItem.title,
          variantTitle: lineItem.variant_title,
          metadata: JSON.stringify({
            originalQuantity: lineItem.quantity,
            price: lineItem.price,
            productId: lineItem.product_id,
            variantId: lineItem.variant_id,
            fulfillmentService: lineItem.fulfillment_service
          })
        }
      });

      return transaction;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to create inventory transaction: ${error.message}`,
        { inventoryRequirementId, orderId, lineItemId, transactionType }
      );
    }
  }

  /**
   * Get inventory requirements for a shop
   * @param {string} shop - Shop identifier
   * @param {object} options - Query options
   * @returns {Promise<object>} - Inventory requirements grouped by category
   */
  async getInventoryRequirements(shop, options = {}) {
    try {
      await this.ensureInitialized();

      const where = { shop };

      // Add category filter if specified
      if (options.category) {
        where.category = options.category;
      }

      // Add minimum quantity filter
      if (options.minQuantity !== undefined) {
        where.requiredQuantity = { gte: options.minQuantity };
      }

      const requirements = await this.prisma.inventoryRequirement.findMany({
        where,
        orderBy: [
          { category: 'asc' },
          { garmentCode: 'asc' },
          { color: 'asc' },
          { size: 'asc' }
        ],
        include: {
          transactions: options.includeTransactions ? {
            orderBy: { createdAt: 'desc' },
            take: options.transactionLimit || 10
          } : false
        }
      });

      // Group by category
      const grouped = {
        regular: requirements.filter(r => r.category === 'regular'),
        engraving: requirements.filter(r => r.category === 'engraving')
      };

      return grouped;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get inventory requirements: ${error.message}`,
        { shop, options }
      );
    }
  }

  /**
   * Get inventory requirements for all shops (admin function)
   * @param {object} options - Query options
   * @returns {Promise<object>} - Inventory requirements grouped by category
   */
  async getAllInventoryRequirements(options = {}) {
    try {
      await this.ensureInitialized();

      const where = {};

      // Add category filter if specified
      if (options.category) {
        where.category = options.category;
      }

      // Add minimum quantity filter
      if (options.minQuantity !== undefined) {
        where.requiredQuantity = { gte: options.minQuantity };
      }

      const requirements = await this.prisma.inventoryRequirement.findMany({
        where,
        orderBy: [
          { shop: 'asc' },
          { category: 'asc' },
          { garmentCode: 'asc' },
          { color: 'asc' },
          { size: 'asc' }
        ],
        include: {
          transactions: options.includeTransactions ? {
            orderBy: { createdAt: 'desc' },
            take: options.transactionLimit || 10
          } : false
        }
      });

      // Group by category
      const grouped = {
        regular: requirements.filter(r => r.category === 'regular'),
        engraving: requirements.filter(r => r.category === 'engraving')
      };

      return grouped;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get all inventory requirements: ${error.message}`,
        { options }
      );
    }
  }

  /**
   * Extract shop identifier from order
   * @param {object} order - Shopify order object
   * @returns {string} - Shop identifier
   */
  extractShopFromOrder(order) {
    // This should match the shop identification logic used elsewhere in the app
    // You may need to adjust this based on how shops are identified in your system
    return order.shop || order.shop_domain || 'unknown';
  }

  /**
   * Process bulk orders for inventory (used during initial bulk query)
   * @param {Array} orders - Array of Shopify orders
   * @param {string} shop - Shop identifier
   * @returns {Promise<object>} - Bulk processing results
   */
  async processBulkOrdersForInventory(orders, shop) {
    const results = {
      totalOrders: orders.length,
      processedOrders: 0,
      skippedOrders: 0,
      totalLineItems: 0,
      processedLineItems: 0,
      skippedLineItems: 0,
      errors: []
    };

    for (const order of orders) {
      try {
        // Determine transaction type based on fulfillment status
        const transactionType = this.determineTransactionTypeFromOrder(order);

        const orderResult = await this.processOrderForInventory(order, transactionType);

        results.processedOrders++;
        results.totalLineItems += (order.line_items || []).length;
        results.processedLineItems += orderResult.processed;
        results.skippedLineItems += orderResult.skipped;

        if (orderResult.errors.length > 0) {
          results.errors.push(...orderResult.errors);
        }
      } catch (error) {
        results.skippedOrders++;
        results.errors.push({
          orderId: order.id,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Determine transaction type from order fulfillment status
   * @param {object} order - Shopify order object
   * @returns {string} - Transaction type
   */
  determineTransactionTypeFromOrder(order) {
    // If order is fulfilled, we need to account for both creation and fulfillment
    // For bulk processing, we'll handle this by processing unfulfilled quantities only
    if (order.fulfillment_status === 'fulfilled') {
      return 'order_created'; // We'll handle fulfillments separately
    }

    if (order.cancelled_at) {
      return 'order_cancelled';
    }

    return 'order_created';
  }

  /**
   * Clean up inventory requirements with zero quantity
   * @param {string} shop - Shop identifier (optional)
   * @returns {Promise<number>} - Number of records cleaned up
   */
  async cleanupZeroQuantityRequirements(shop = null) {
    await this.ensureInitialized();

    const where = { requiredQuantity: { lte: 0 } };
    if (shop) {
      where.shop = shop;
    }

    const result = await this.prisma.inventoryRequirement.deleteMany({ where });
    return result.count;
  }
}
