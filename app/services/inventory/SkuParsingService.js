/**
 * SKU Parsing Service
 *
 * This service handles the complex logic of extracting garment codes, colors, and sizes
 * from SKUs. It analyzes hyphen-delimited SKU parts to identify the correct garment code
 * and extract variant information.
 */

import { BusinessLogicError } from '../../lib/errors/AppError.js';

export class SkuParsingService {
  constructor() {
    // Common garment codes that appear in SKUs
    this.garmentCodes = new Set([
      'NXTLVL6210', 'NXTLVL6211', 'SS3000', 'IND4000', 'ROTHCO', 'ROTHCO5050',
      'DT6000', 'ST350', 'PARA200', 'OG152', 'OG101', 'CC17170', 'CC17171',
      'CC6030', 'EB250', 'EZA', 'R112', 'BELLA3480', '3480CVC', 'INDDUFBAG'
    ]);

    // Size patterns
    this.sizePatterns = [
      'XS', 'S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL',
      'Small', 'Medium', 'Large', 'X-Large', '2X-Large', '3X-Large',
      'OSFA', 'OSFM', 'Adjustable', 'Default Title'
    ];

    // Color patterns (common colors found in variant titles)
    this.colorPatterns = [
      'Black', 'White', 'Grey', 'Gray', 'Navy', 'Blue', 'Red', 'Green', 'OD Green',
      'Charcoal', 'Heather', 'Coyote', 'Brown', 'Tan', 'Khaki', 'Olive', 'Purple',
      'Yellow', 'Orange', 'Pink', 'Maroon', 'Cardinal', 'Forest', 'Royal',
      'Blacktop', 'Greystone', 'Stone Grey', 'Navy Heather', 'Cool Blue',
      'Spar Blue', 'Loden Green', 'Moss Green', 'Pepper', 'Espresso', 'Granite',
      'Indigo', 'Sand', 'Light Olive', 'AR 670-1 Coyote', 'Coyote Brown',
      'Multicam', 'Black Multicam', 'Tiger Stripe', 'Woodland'
    ];
  }

  /**
   * Parse SKU to extract garment code, color, and size information
   * @param {string} sku - Product SKU
   * @param {string} variantTitle - Product variant title (contains size/color info)
   * @param {string} productType - Product type
   * @returns {object} - Parsed information
   */
  parseSkuForInventory(sku, variantTitle = '', productType = '') {
    try {
      // Skip items that should not be included in inventory
      if (this.shouldSkipItem(sku, productType)) {
        return {
          shouldSkip: true,
          reason: 'Item excluded from inventory requirements'
        };
      }

      // Determine category (regular vs engraving)
      const category = this.determineCategory(sku);

      // Extract garment code from SKU
      const garmentCode = this.extractGarmentCode(sku);

      // Extract color and size from variant title
      const { color, size } = this.extractColorAndSize(variantTitle);

      return {
        shouldSkip: false,
        garmentCode,
        color,
        size,
        category,
        originalSku: sku,
        originalVariantTitle: variantTitle
      };

    } catch (error) {
      throw new BusinessLogicError(
        `Failed to parse SKU for inventory: ${error.message}`,
        { sku, variantTitle, productType }
      );
    }
  }

  /**
   * Determine if an item should be skipped from inventory requirements
   * @param {string} sku - Product SKU
   * @param {string} productType - Product type
   * @returns {boolean} - Whether to skip this item
   */
  shouldSkipItem(sku, productType) {
    if (!sku) return true;

    const skuUpper = sku.toUpperCase();

    // Skip flags, patches, stickers, decals, or fulfillment items starting with F- or *F-
    if (skuUpper.startsWith('F-') || skuUpper.startsWith('*F-')) {
      return true;
    }

    // Skip other non-garment items
    const skipTypes = [
      'FLAG', 'PATCH', 'STICKER', 'DECAL', 'COIN', 'PIN', 'CUFFLINKS',
      'BRACELET', 'CANVAS', 'PRINT', 'POSTER', 'MUG', 'TUMBLER', 'HUMIDOR',
      'PLAYING CARDS', 'DESK MAT', 'WOVEN BLANKET', 'FLEECE BLANKET'
    ];

    return skipTypes.some(type => 
      productType.toUpperCase().includes(type) || 
      skuUpper.includes(type.replace(/\s+/g, '-'))
    );
  }

  /**
   * Determine category based on SKU prefix
   * @param {string} sku - Product SKU
   * @returns {string} - Category ('regular' or 'engraving')
   */
  determineCategory(sku) {
    const skuUpper = sku.toUpperCase();

    // Laser engraving items
    if (skuUpper.startsWith('L-') || skuUpper.startsWith('*L-')) {
      return 'engraving';
    }

    // UV engraving items
    if (skuUpper.startsWith('*UVT-') || skuUpper.startsWith('*UVB-') || 
        skuUpper.startsWith('*UVD-')) {
      return 'engraving';
    }

    return 'regular';
  }

  /**
   * Extract garment code from SKU
   * @param {string} sku - Product SKU
   * @returns {string} - Garment code
   */
  extractGarmentCode(sku) {
    const parts = sku.split('-');
    
    // For most SKUs, the garment code is the second to last part
    // But we need to check if it's a known garment code
    for (let i = parts.length - 2; i >= 0; i--) {
      const part = parts[i];
      if (this.garmentCodes.has(part)) {
        return part;
      }
    }

    // If no known garment code found, try to identify by pattern
    // Look for parts that look like garment codes (alphanumeric, 4+ chars)
    for (let i = parts.length - 2; i >= 0; i--) {
      const part = parts[i];
      if (part && part.length >= 4 && /^[A-Z0-9]+$/.test(part)) {
        // Skip numeric-only parts (likely size indicators)
        if (!/^\d+$/.test(part)) {
          return part;
        }
      }
    }

    // Fallback: use the product base name (everything before the last dash)
    if (parts.length > 1) {
      return parts.slice(0, -1).join('-');
    }

    return sku; // Last resort
  }

  /**
   * Extract color and size from variant title
   * @param {string} variantTitle - Variant title (e.g., "Large / Black", "XL / OD Green")
   * @returns {object} - Color and size information
   */
  extractColorAndSize(variantTitle) {
    if (!variantTitle || variantTitle === 'Default Title') {
      return { color: 'Default', size: 'Default' };
    }

    // Split by common delimiters
    const parts = variantTitle.split(/[\/,\-\|]/).map(part => part.trim());

    let size = 'Unknown';
    let color = 'Unknown';

    // Try to identify size and color from parts
    for (const part of parts) {
      // Check if this part is a size
      if (this.isSize(part)) {
        size = this.normalizeSize(part);
      }
      // Check if this part is a color
      else if (this.isColor(part)) {
        color = this.normalizeColor(part);
      }
    }

    // If we couldn't identify size/color, try more flexible matching
    if (size === 'Unknown' || color === 'Unknown') {
      const flexible = this.flexibleColorSizeExtraction(variantTitle);
      if (size === 'Unknown') size = flexible.size;
      if (color === 'Unknown') color = flexible.color;
    }

    return { color, size };
  }

  /**
   * Check if a string represents a size
   * @param {string} str - String to check
   * @returns {boolean} - Whether it's a size
   */
  isSize(str) {
    return this.sizePatterns.some(pattern => 
      str.toUpperCase() === pattern.toUpperCase()
    );
  }

  /**
   * Check if a string represents a color
   * @param {string} str - String to check
   * @returns {boolean} - Whether it's a color
   */
  isColor(str) {
    return this.colorPatterns.some(pattern => 
      str.toLowerCase().includes(pattern.toLowerCase()) ||
      pattern.toLowerCase().includes(str.toLowerCase())
    );
  }

  /**
   * Normalize size string
   * @param {string} size - Size string
   * @returns {string} - Normalized size
   */
  normalizeSize(size) {
    const sizeMap = {
      'SMALL': 'S',
      'MEDIUM': 'M', 
      'LARGE': 'L',
      'X-LARGE': 'XL',
      '2X-LARGE': '2XL',
      '3X-LARGE': '3XL'
    };

    const normalized = sizeMap[size.toUpperCase()] || size;
    return normalized;
  }

  /**
   * Normalize color string
   * @param {string} color - Color string
   * @returns {string} - Normalized color
   */
  normalizeColor(color) {
    // Basic normalization - could be expanded
    return color.trim();
  }

  /**
   * More flexible color and size extraction for complex variant titles
   * @param {string} variantTitle - Variant title
   * @returns {object} - Color and size
   */
  flexibleColorSizeExtraction(variantTitle) {
    let size = 'Unknown';
    let color = 'Unknown';

    // Look for size patterns anywhere in the string
    for (const sizePattern of this.sizePatterns) {
      const regex = new RegExp(`\\b${sizePattern}\\b`, 'i');
      if (regex.test(variantTitle)) {
        size = this.normalizeSize(sizePattern);
        break;
      }
    }

    // Look for color patterns anywhere in the string
    for (const colorPattern of this.colorPatterns) {
      const regex = new RegExp(`\\b${colorPattern}\\b`, 'i');
      if (regex.test(variantTitle)) {
        color = this.normalizeColor(colorPattern);
        break;
      }
    }

    return { color, size };
  }
}
