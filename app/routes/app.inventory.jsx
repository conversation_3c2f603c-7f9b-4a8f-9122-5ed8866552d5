import { useState, useCallback, useMemo } from "react";

import { useLoaderData, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  DataTable,
  Text,
  Badge,
  Spinner,
  EmptyState,
  Filters,
  ChoiceList,
  TextField,
  Button,
  ButtonGroup,
  Tabs,
  Box,
  InlineStack,
  BlockStack
} from "@shopify/polaris";

import { InventoryCalculationService } from "../services/inventory/InventoryCalculationService.js";

export const loader = async ({ request }) => {
  // Import server-only functions inside the loader
  const { authenticateMultistore } = await import("../shopify.multistore.server");

  // Use multistore authentication
  const result = await authenticateMultistore(request);

  // If result is a Response (redirect), throw it to let Remix handle it
  if (result instanceof Response) {
    throw result;
  }

  const { session } = result;

  try {
    const inventoryService = new InventoryCalculationService();

    // Import server-only functions to check if this is an admin shop
    const { getStoreConfig } = await import("../shopify.multistore.server");
    const storeConfig = await getStoreConfig(session.shop);
    const isAdmin = storeConfig?.isAdmin || false;

    let inventoryData;

    if (isAdmin) {
      // Admin shop: Get inventory requirements for ALL stores
      inventoryData = await inventoryService.getAllInventoryRequirements({
        minQuantity: 0, // Include all items, even with 0 quantity
        includeTransactions: false // Don't include transaction history for performance
      });
    } else {
      // Regular shop: Get inventory requirements for current shop only
      inventoryData = await inventoryService.getInventoryRequirements(
        session.shop,
        {
          minQuantity: 0, // Include all items, even with 0 quantity
          includeTransactions: false // Don't include transaction history for performance
        }
      );
    }


    return Response.json({
      inventoryData,
      shop: session.shop,
      isAdmin,
      success: true
    });

  } catch (error) {
    console.error("Error loading inventory requirements:", error);

    return Response.json({
      inventoryData: { regular: [], engraving: [] },
      shop: session.shop,
      success: false,
      error: error.message || error.toString() || "Unknown error occurred"
    });
  }
};

export default function InventoryPage() {
  const { inventoryData, shop, isAdmin, success, error } = useLoaderData();
  const navigation = useNavigation();
  const isLoading = navigation.state === "loading";

  // State for filtering and search
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilters, setSelectedFilters] = useState({
    garmentCode: [],
    color: [],
    size: []
  });

  // Tab configuration
  const tabs = [
    {
      id: "regular",
      content: "Regular Items",
      accessibilityLabel: "Regular inventory items",
      panelID: "regular-items-panel"
    },
    {
      id: "engraving",
      content: "Engraving Items",
      accessibilityLabel: "Engraving inventory items",
      panelID: "engraving-items-panel"
    }
  ];

  // Get current data based on selected tab
  const currentData = selectedTab === 0 ? inventoryData.regular : inventoryData.engraving;
  const currentCategory = selectedTab === 0 ? "regular" : "engraving";

  // Extract unique filter options from current data
  const filterOptions = useMemo(() => {
    const garmentCodes = [...new Set(currentData.map(item => item.garmentCode))].sort();
    const colors = [...new Set(currentData.map(item => item.color))].sort();
    const sizes = [...new Set(currentData.map(item => item.size))].sort();
    const shops = isAdmin ? [...new Set(currentData.map(item => item.shop))].sort() : [];

    const options = {
      garmentCode: garmentCodes.map(code => ({ label: code, value: code })),
      color: colors.map(color => ({ label: color, value: color })),
      size: sizes.map(size => ({ label: size, value: size }))
    };

    if (isAdmin) {
      options.shop = shops.map(shop => ({
        label: formatShopName(shop),
        value: shop
      }));
    }

    return options;
  }, [currentData, isAdmin]);

  // Filter and search data
  const filteredData = useMemo(() => {
    let filtered = currentData;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.garmentCode.toLowerCase().includes(query) ||
        item.color.toLowerCase().includes(query) ||
        item.size.toLowerCase().includes(query)
      );
    }

    // Apply selected filters
    if (selectedFilters.garmentCode.length > 0) {
      filtered = filtered.filter(item => selectedFilters.garmentCode.includes(item.garmentCode));
    }
    if (selectedFilters.color.length > 0) {
      filtered = filtered.filter(item => selectedFilters.color.includes(item.color));
    }
    if (selectedFilters.size.length > 0) {
      filtered = filtered.filter(item => selectedFilters.size.includes(item.size));
    }

    return filtered;
  }, [currentData, searchQuery, selectedFilters]);

  // Calculate totals
  const totals = useMemo(() => {
    const totalItems = filteredData.length;
    const totalQuantity = filteredData.reduce((sum, item) => sum + item.requiredQuantity, 0);
    const itemsWithRequirements = filteredData.filter(item => item.requiredQuantity > 0).length;

    return { totalItems, totalQuantity, itemsWithRequirements };
  }, [filteredData]);

  // Helper function to format shop name
  const formatShopName = (shopDomain) => {
    if (!shopDomain) return 'Unknown';
    return shopDomain
      .replace('.myshopify.com', '')
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  // Prepare data for DataTable
  const tableRows = filteredData.map(item => {
    const baseRow = [
      item.garmentCode,
      item.color,
      item.size,
      <Badge tone={item.requiredQuantity > 0 ? "attention" : "success"}>
        {item.requiredQuantity}
      </Badge>,
      new Date(item.updatedAt).toLocaleDateString()
    ];

    // Add shop column for admin view
    if (isAdmin) {
      baseRow.unshift(formatShopName(item.shop));
    }

    return baseRow;
  });

  const tableHeadings = isAdmin
    ? ["Shop", "Garment Code", "Color", "Size", "Required Quantity", "Last Updated"]
    : ["Garment Code", "Color", "Size", "Required Quantity", "Last Updated"];

  // Filter handlers
  const handleFiltersChange = useCallback((filters) => {
    setSelectedFilters(filters);
  }, []);

  const handleSearchChange = useCallback((value) => {
    setSearchQuery(value);
  }, []);

  const handleFiltersClearAll = useCallback(() => {
    setSelectedFilters({
      garmentCode: [],
      color: [],
      size: []
    });
    setSearchQuery("");
  }, []);

  // Filter components
  const filters = [
    {
      key: "garmentCode",
      label: "Garment Code",
      filter: (
        <ChoiceList
          title="Garment Code"
          titleHidden
          choices={filterOptions.garmentCode}
          selected={selectedFilters.garmentCode}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, garmentCode: value })}
          allowMultiple
        />
      ),
      shortcut: true
    },
    {
      key: "color",
      label: "Color",
      filter: (
        <ChoiceList
          title="Color"
          titleHidden
          choices={filterOptions.color}
          selected={selectedFilters.color}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, color: value })}
          allowMultiple
        />
      ),
      shortcut: true
    },
    {
      key: "size",
      label: "Size",
      filter: (
        <ChoiceList
          title="Size"
          titleHidden
          choices={filterOptions.size}
          selected={selectedFilters.size}
          onChange={(value) => handleFiltersChange({ ...selectedFilters, size: value })}
          allowMultiple
        />
      ),
      shortcut: true
    }
  ];

  const appliedFilters = [];
  if (selectedFilters.garmentCode.length > 0) {
    appliedFilters.push({
      key: "garmentCode",
      label: `Garment Code: ${selectedFilters.garmentCode.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, garmentCode: [] })
    });
  }
  if (selectedFilters.color.length > 0) {
    appliedFilters.push({
      key: "color",
      label: `Color: ${selectedFilters.color.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, color: [] })
    });
  }
  if (selectedFilters.size.length > 0) {
    appliedFilters.push({
      key: "size",
      label: `Size: ${selectedFilters.size.join(", ")}`,
      onRemove: () => handleFiltersChange({ ...selectedFilters, size: [] })
    });
  }

  if (!success) {
    return (
      <Page title="Inventory Requirements">
        <Layout>
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">Error Loading Inventory Data</Text>
                <Text>{error || "An unknown error occurred while loading inventory requirements."}</Text>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </Page>
    );
  }

  return (
    <Page
      title="Inventory Requirements"
      subtitle={isAdmin ? "Based on orders for all client stores" : `Based on orders for ${shop}`}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Tabs tabs={tabs} selected={selectedTab} onSelect={setSelectedTab}>
              <Box padding="400">
                {isLoading ? (
                  <div style={{ textAlign: "center", padding: "2rem" }}>
                    <Spinner size="large" />
                    <Text>Loading inventory requirements...</Text>
                  </div>
                ) : (
                  <BlockStack gap="400">
                    {/* Summary Stats */}
                    <InlineStack gap="400" align="space-between">
                      <Text variant="headingMd" as="h3">
                        {currentCategory === "regular" ? "Regular Items" : "Engraving Items"}
                      </Text>
                      <InlineStack gap="200">
                        <Badge tone="info">{totals.totalItems} total items</Badge>
                        <Badge tone="attention">{totals.itemsWithRequirements} need inventory</Badge>
                        <Badge tone="warning">{totals.totalQuantity} total quantity needed</Badge>
                      </InlineStack>
                    </InlineStack>

                    {/* Filters */}
                    <Filters
                      queryValue={searchQuery}
                      queryPlaceholder="Search garment codes, colors, or sizes..."
                      filters={filters}
                      appliedFilters={appliedFilters}
                      onQueryChange={handleSearchChange}
                      onQueryClear={() => setSearchQuery("")}
                      onClearAll={handleFiltersClearAll}
                    />

                    {/* Data Table */}
                    {filteredData.length === 0 ? (
                      <EmptyState
                        heading={searchQuery || appliedFilters.length > 0 ? "No items match your filters" : "No inventory requirements found"}
                        image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                      >
                        {searchQuery || appliedFilters.length > 0 ? (
                          <p>Try adjusting your search or filters to find what you're looking for.</p>
                        ) : (
                          <p>Inventory requirements will appear here once orders are processed.</p>
                        )}
                      </EmptyState>
                    ) : (
                      <DataTable
                        columnContentTypes={["text", "text", "text", "text", "text"]}
                        headings={tableHeadings}
                        rows={tableRows}
                        pagination={{
                          hasNext: false,
                          hasPrevious: false,
                          label: `Showing ${filteredData.length} of ${currentData.length} items`
                        }}
                      />
                    )}
                  </BlockStack>
                )}
              </Box>
            </Tabs>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
