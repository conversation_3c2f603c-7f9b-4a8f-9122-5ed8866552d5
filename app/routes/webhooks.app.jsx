import { authenticate } from "../shopify.server";
import { getShopifyAppForStore } from "../shopify.multistore.server.js";
import db from "../db.server"
import { container } from "../lib/container/ServiceContainer.server.js";
import { hasProcessedWebhook, markWebhookProcessed, hasProcessedOrder, checkAndMarkEventProcessed } from "../models/ProcessedWebhook.server";

//! TODO - Set up webhooks to process updated, cancelled, and fulfilled orders
// fulfilled orders need to have the calculated shipping costs added for the day they were fulfilled

export const action = async({ request }) => {
    // Extract shop domain from webhook headers first
    const shopDomain = request.headers.get('X-Shopify-Shop-Domain');

    let shop, topic, payload, session;

    if (shopDomain) {
        // Use multistore authentication for the specific shop
        const shopifyAppInstance = getShopifyAppForStore(shopDomain);
        if (shopifyAppInstance) {
            console.log(`Using multistore authentication for shop: ${shopDomain}`);
            const result = await shopifyAppInstance.authenticate.webhook(request);
            shop = result.shop;
            topic = result.topic;
            payload = result.payload;
            session = result.session;
        } else {
            console.warn(`No multistore config found for shop: ${shopDomain}, falling back to default`);
            const result = await authenticate.webhook(request);
            shop = result.shop;
            topic = result.topic;
            payload = result.payload;
            session = result.session;
        }
    } else {
        // Fallback to default authentication if no shop domain header
        console.warn('No X-Shopify-Shop-Domain header found, using default authentication');
        const result = await authenticate.webhook(request);
        shop = result.shop;
        topic = result.topic;
        payload = result.payload;
        session = result.session;
    }

    // The OrderProcessingService handles its own GraphQL queries, so no admin client needed here

    // Extract the event ID and subscription ID from the headers
    const eventId = request.headers.get('X-Shopify-Event-Id') || null;
    const subscriptionId = request.headers.get('X-Shopify-Webhook-Id') || 'unknown';

    // Log the webhook details including event ID and subscription ID
    console.log(`Received ${topic} webhook for ${shop} with event ID: ${eventId}, subscription ID: ${subscriptionId}`);

    if (!eventId) {
        console.error('No X-Shopify-Event-Id header found in webhook request');
    }

    // Generate a unique ID for this webhook that includes the subscription ID
    // This ensures we don't process the same order twice from different subscriptions
    let webhookId;

    if (topic.startsWith('ORDERS_') && payload.id) {
        // For orders, include both order ID and subscription ID to ensure uniqueness
        webhookId = `order-${String(payload.id)}-${subscriptionId}`;
    } else if (topic.startsWith('PRODUCTS_') && payload.id) {
        // For products, include both product ID and subscription ID
        webhookId = `product-${String(payload.id)}-${subscriptionId}`;
    } else {
        // For other webhooks, generate a random ID that includes the subscription ID
        webhookId = `${topic}-${subscriptionId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    }

    // Log the webhook ID for debugging
    console.log(`Generated webhookId: ${webhookId} (type: ${typeof webhookId})`);
    console.log(`Original payload.id: ${payload.id} (type: ${typeof payload.id})`);

    // Ensure webhookId is always a string
    webhookId = String(webhookId);

    // Extract the raw order ID if this is an order webhook
    let orderId = null;
    if (topic.startsWith('ORDERS_') && payload.id) {
        orderId = String(payload.id);
    }

    // Use atomic check-and-mark operation for event ID to prevent race conditions
    if (eventId) {
        try {
            // This will both check if the event has been processed AND mark it as processed in one operation
            // This prevents race conditions where two webhooks with the same event ID arrive close together
            const eventAlreadyProcessed = await checkAndMarkEventProcessed(shop, eventId, topic, orderId);
            if (eventAlreadyProcessed) {
                console.log(`Event ${eventId} has already been processed. Skipping.`);
                return new Response(null, { status: 200 });
            }
            // If we get here, the event was not previously processed but has now been marked as processed
            console.log(`Event ${eventId} is now marked as processed. Continuing with processing.`);
        } catch (error) {
            // If there's an error with the atomic operation, log it but continue with traditional checks
            console.error(`Error with atomic check-and-mark: ${error.message}`);
            // Fall through to traditional checks
        }
    } else {
        // No event ID available, use traditional checks
        console.warn('No event ID available in webhook. Using fallback deduplication methods.');

        // Fallback to checking the webhook ID if no event ID is available
        try {
            const alreadyProcessed = await hasProcessedWebhook(shop, webhookId);
            if (alreadyProcessed) {
                console.log(`Webhook ${webhookId} for ${topic} has already been processed. Skipping.`);
                return new Response(null, { status: 200 });
            }
        } catch (error) {
            // If there's an error checking the webhook status, log it but continue processing
            console.error(`Error checking webhook status: ${error.message}`);
            // Don't return here, continue processing the webhook
        }

        // For order webhooks, also check if we've already processed this order from a different subscription
        if (orderId) {
            try {
                const orderAlreadyProcessed = await hasProcessedOrder(shop, orderId);
                if (orderAlreadyProcessed) {
                    console.log(`Order ${orderId} has already been processed from a different webhook. Skipping.`);

                    // Still mark this webhook as processed to avoid future processing
                    await markWebhookProcessed(shop, topic, webhookId, orderId, subscriptionId, eventId);

                    return new Response(null, { status: 200 });
                }
            } catch (error) {
                // If there's an error checking the order status, log it but continue processing
                console.error(`Error checking order status: ${error.message}`);
                // Don't return here, continue processing the webhook
            }
        }

        // If we get here with no event ID, we need to mark this webhook as processed now
        // to prevent race conditions with other webhooks
        try {
            await markWebhookProcessed(shop, topic, webhookId, orderId, subscriptionId, eventId);
        } catch (error) {
            // If there's an error marking the webhook as processed, log it but continue processing
            console.error(`Error marking webhook as processed: ${error.message}`);
        }
    }

    switch (topic) {
        case "APP_UNINSTALLED":
            if (session) {
                await db.session.deleteMany({ where: { shop }})
            }
            break;
        case "ORDERS_CREATE":
            /* //! PAYLOAD - Same for Created, Updated, Fulfilled, and Cancelled
                {
                    id: string
                    admin_graphql_api_id: string (ID) - the full GID version of the order ID (gid://shopify/Order/<id>)
                    cancel_reason: string
                    fulfillment_status: string
                    line_items: [
                        {
                            id: string
                            admin_graphql_api_id: string (ID)
                            fulfillment_status: string
                            title: string
                            variant_id: string
                            variant_title: string
                            sku: string
                            quantity: int
                        }
                    ]
                }
            */


            // Skip fulfillment info queries for now - the OrderProcessingService handles this internally
            console.log(`Processing order ${payload.id} for shop ${shop} - skipping fulfillment info queries`);

            // Use the OrderProcessingService from container to process the entire order
            try {
                const orderProcessingService = await container.resolve('orderProcessingService');

                console.log(`Processing order ${payload.id} for shop ${shop} using OrderProcessingService`);

                const result = await orderProcessingService.processOrder(
                    payload.id,
                    shop,
                    {
                        skipValidation: false, // Validate the order
                        dryRun: false, // Actually process and save
                        forceReprocess: false, // Don't reprocess if already done
                        session: session // Pass the session for authentication
                    }
                );

                if (result.success) {
                    // Create a detailed log with line item information
                    const logResult = {
                        ...result,
                        result: {
                            ...result.result,
                            lineItems: result.result.lineItems?.map(item => ({
                                sku: item.sku || 'N/A',
                                title: item.title || 'N/A',
                                quantity: item.quantity || 0,
                                costOfGoods: item.costOfGoods || 0,
                                skipped: item.skipped || false,
                                reason: item.reason || null,
                                processed: item.processed || false
                            })) || []
                        }
                    };

                    console.log(`Successfully processed order ${payload.id}:`, JSON.stringify(logResult, null, 2));

                    // Also process the order for inventory requirements
                    try {
                        const { InventoryCalculationService } = await import('../services/inventory/InventoryCalculationService.js');
                        const inventoryService = new InventoryCalculationService();

                        const inventoryResult = await inventoryService.processOrderForInventory(payload, 'order_created');
                        console.log(`Inventory processing for order ${payload.id}:`, {
                            processed: inventoryResult.processed,
                            skipped: inventoryResult.skipped,
                            errors: inventoryResult.errors.length
                        });
                    } catch (inventoryError) {
                        console.error(`Error processing inventory for order ${payload.id}:`, inventoryError);
                        // Don't fail the webhook if inventory processing fails
                    }
                } else {
                    console.error(`Failed to process order ${payload.id}:`, result);
                }

            } catch (error) {
                console.error(`Error processing order ${payload.id} with OrderProcessingService:`, error);

                // For validation errors, log the detailed information more clearly
                if (error.name === 'ValidationError' || error.name === 'BusinessLogicError') {
                    console.log(`Order ${payload.id} validation details:`);
                    console.log(`- Shop: ${shop}`);
                    console.log(`- Order ID: ${payload.id}`);
                    console.log(`- Error Type: ${error.name}`);
                    console.log(`- Error Message: ${error.message}`);

                    // If the error message contains line item details, format them nicely
                    if (error.message.includes('Line Item')) {
                        console.log(`- Detailed Line Item Analysis:`);
                        const lines = error.message.split('\n');
                        lines.forEach(line => {
                            if (line.trim()) {
                                console.log(`  ${line.trim()}`);
                            }
                        });
                    }

                    // Log order line items for context
                    if (payload.line_items && payload.line_items.length > 0) {
                        console.log(`- Order contains ${payload.line_items.length} line items:`);
                        payload.line_items.forEach((item, index) => {
                            console.log(`  ${index + 1}. "${item.title}" (SKU: ${item.sku || 'No SKU'}, Fulfillment: ${item.fulfillment_service || 'manual'}, Qty: ${item.quantity})`);
                        });
                    }
                }

                // Log the error for debugging but don't throw to avoid webhook failures
                // The OrderProcessingService already handles logging internally
            }


            break;
        case "PRODUCTS_UPDATE":
            try {
                // Get services from container
                const transactionProcessingService = await container.resolve('transactionProcessingService');

                // Get unprocessables for this shop
                const unprocessableStats = await transactionProcessingService.getUnprocessableStatistics(shop);
                if (!unprocessableStats || unprocessableStats.total === 0) {
                    break;
                }

                let productType = payload.product_type;
                let productId = payload.id;

                // Process each variant
                for (const variant of payload.variants) {
                    const variantId = variant.id;

                    // Try to reprocess any matching unprocessable items
                    const result = await transactionProcessingService.retrySpecificUnprocessable(
                        shop,
                        variantId,
                        productId,
                        productType
                    );

                    if (result) {
                        console.log(`Successfully reprocessed unprocessable item for variant ${variantId} of product ${productId}`);
                    }
                }
            } catch (error) {
                console.error(`Error in PRODUCTS_UPDATE webhook: ${error.message}`);
            }
            break
        case "ORDERS_DELETE":
            // occurs when an order is deleted
            //! payload: { id: string }
        case "ORDERS_UPDATED":
            //! orders/create, orders/delete, orders/fulfilled and orders/updated return the same payload
        case "ORDERS_EDITED":
            // occurs when an order is edited
            // payload:
            /*
                {
                    "order_edit": {
                        id: string
                        created_at: datetime
                        committed_at: datetime
                        order_id: string
                        user_id: null
                        line_items: {
                            additions: [
                                {
                                    id: string
                                    delta: int
                                }
                            ],
                            removals: [
                                {
                                    id: string
                                    delta: int
                                }
                            ]
                        }
                    }
                }
            */
        case "ORDERS_FULFILLED":
            //! orders/create, orders/delete, orders/fulfilled and orders/updated return the same payload
            try {
                const { InventoryCalculationService } = await import('../services/inventory/InventoryCalculationService.js');
                const inventoryService = new InventoryCalculationService();

                const inventoryResult = await inventoryService.processOrderForInventory(payload, 'order_fulfilled');
                console.log(`Inventory processing for fulfilled order ${payload.id}:`, {
                    processed: inventoryResult.processed,
                    skipped: inventoryResult.skipped,
                    errors: inventoryResult.errors.length
                });
            } catch (inventoryError) {
                console.error(`Error processing inventory for fulfilled order ${payload.id}:`, inventoryError);
                // Don't fail the webhook if inventory processing fails
            }
            break;
        case "ORDERS_CANCELLED":
            //! orders/create, orders/delete, orders/fulfilled and orders/updated return the same payload
            try {
                const { InventoryCalculationService } = await import('../services/inventory/InventoryCalculationService.js');
                const inventoryService = new InventoryCalculationService();

                const inventoryResult = await inventoryService.processOrderForInventory(payload, 'order_cancelled');
                console.log(`Inventory processing for cancelled order ${payload.id}:`, {
                    processed: inventoryResult.processed,
                    skipped: inventoryResult.skipped,
                    errors: inventoryResult.errors.length
                });
            } catch (inventoryError) {
                console.error(`Error processing inventory for cancelled order ${payload.id}:`, inventoryError);
                // Don't fail the webhook if inventory processing fails
            }
            break;

        case "CUSTOMERS_DATA_REQUEST":
        case "CUSTOMERS_REDACT":
        case "SHOP_REDACT":
        default:
            throw new Response("Unhandled webhook topic", { status: 404 })
    }

    // Only mark the webhook as processed if we didn't already do it with the event ID
    // (If we used checkAndMarkEventProcessed successfully, we don't need to do this again)
    if (!eventId) {
        // We already marked it as processed earlier in the code for non-event ID webhooks
        console.log('Webhook was already marked as processed earlier.');
    } else {
        // For event ID webhooks, we don't need to mark it again since checkAndMarkEventProcessed did it
        console.log(`Event ${eventId} was already marked as processed by checkAndMarkEventProcessed.`);
    }

    return new Response(null, { status: 200 });
}


// products/create - can pull id, title, variants titles and skus, etc.
// products/delete
// products/update
