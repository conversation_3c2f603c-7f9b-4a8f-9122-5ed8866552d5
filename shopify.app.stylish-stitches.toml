# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "4f62408a57ff8202871bbff18316a1d6"
application_url = "https://satyr-up-vulture.ngrok-free.app/"
embedded = true
name = "AUI Stylish Stitches"
handle = "aui-stylish-stitches"

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "orders/create", "products/update" ]
  uri = "/webhooks/app"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders,read_products,write_orders"

[auth]
redirect_urls = [
  "https://satyr-up-vulture.ngrok-free.app/auth/callback",
  "https://satyr-up-vulture.ngrok-free.app/auth/shopify/callback",
  "https://satyr-up-vulture.ngrok-free.app/api/auth/callback"
]

[pos]
embedded = false
